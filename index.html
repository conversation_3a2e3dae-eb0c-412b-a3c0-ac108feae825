<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> - <PERSON></title>
    <meta name="description" content="Portfolio of Larry Bronx - UX/UI Designer and Frontend Developer specializing in modern web design and user experience.">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&family=PT+Serif:ital,wght@0,400;0,700;1,400;1,700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link rel="stylesheet" href="styles.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="images/favicon.svg">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-content">
                <div class="nav-selected" id="nav-selected"></div>
                <div class="nav-links">
                    <a href="#home" class="nav-link active" data-section="home">Home</a>
                    <a href="#about" class="nav-link" data-section="about">About</a>
                    <a href="#stack" class="nav-link" data-section="stack">Stack</a>
                    <a href="#services" class="nav-link" data-section="services">Services</a>
                    <a href="#projects" class="nav-link" data-section="projects">Projects</a>
                    <a href="#contact" class="nav-link" data-section="contact">Contact</a>
                </div>
            </div>
            
            <!-- Mobile Menu Button -->
            <div class="mobile-menu-btn" id="mobile-menu-btn">
                <span class="menu-line"></span>
                <span class="menu-line"></span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section id="home" class="hero-section">
            <div class="hero-scroll-anchor"></div>
            
            <!-- Large Name Display (Desktop) -->
            <div class="hero-name-display">
                <div class="scrolling-name">
                    <h1 class="large-name">LARRY BRONX</h1>
                </div>
            </div>
            
            <!-- Profile Section -->
            <div class="profile-section">
                <!-- Client Avatars -->
                <div class="clients-display">
                    <div class="client-avatar">
                        <img src="images/client1.svg" alt="Client 1">
                    </div>
                    <div class="client-avatar">
                        <img src="images/client2.svg" alt="Client 2">
                    </div>
                    <div class="client-avatar">
                        <img src="images/client3.svg" alt="Client 3">
                    </div>
                    <p class="clients-text">80+ Happy Clients</p>
                </div>
                
                <!-- Main Greeting -->
                <div class="hero-greeting">
                    <h1>Hi, I'm <span class="name-highlight">Larry</span>!</h1>
                </div>
                
                <!-- Skills Tags -->
                <div class="skills-container">
                    <div class="skill-tag">UX/UI Expertise</div>
                    <div class="skill-tag">HTML5/CSS3 Mastery</div>
                    <div class="skill-tag">Product Design</div>
                    <div class="skill-tag">Branding</div>
                    <div class="skill-tag">Collaborative Team Player</div>
                </div>
                
                <!-- Profile Image -->
                <div class="profile-image-container">
                    <div class="profile-image-back">
                        <div class="circle-big"></div>
                        <div class="circle-small"></div>
                        <div class="rotating-text">
                            <svg viewBox="0 0 100 100">
                                <path id="circle-path" d="M 50,50 m -37,0 a 37,37 0 1,1 74,0 a 37,37 0 1,1 -74,0" fill="none"/>
                                <text>
                                    <textPath href="#circle-path" startOffset="0%">
                                        SCROLL DOWN • AND KNOW ME BETTER • 
                                    </textPath>
                                </text>
                            </svg>
                        </div>
                    </div>
                    <div class="profile-image">
                        <img src="images/profile.png" alt="Larry Bronx Profile Picture">
                    </div>
                </div>
                
                <!-- CTA Button -->
                <div class="cta-container">
                    <a href="#contact" class="cta-button">
                        <span class="cta-text">Let's Work Together!</span>
                        <div class="cta-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M7 17L17 7M17 7H7M17 7V17"/>
                            </svg>
                        </div>
                    </a>
                </div>
            </div>
            
            <!-- 3D Elements -->
            <div class="floating-elements">
                <div class="floating-element orange-pyramid">
                    <img src="images/orange-pyramid.png" alt="Orange Pyramid">
                </div>
                <div class="floating-element purple-sphere">
                    <img src="images/purple-sphere.png" alt="Purple Sphere">
                </div>
                <div class="floating-element green-cube">
                    <img src="images/green-cube.png" alt="Green Cube">
                </div>
            </div>
        </section>

        <!-- About Section -->
        <section id="about" class="about-section">
            <div class="container">
                <div class="section-header">
                    <h2>About Me</h2>
                    <p class="section-subtitle">Get to know me better</p>
                </div>
                
                <div class="about-content">
                    <div class="about-text">
                        <h3>I'm a passionate UX/UI Designer & Frontend Developer</h3>
                        <p>With over 5 years of experience in creating beautiful and functional digital experiences, I specialize in turning complex problems into simple, elegant solutions.</p>
                        <p>I believe in the power of good design to transform businesses and improve people's lives. My approach combines user-centered design principles with modern development practices.</p>
                        
                        <div class="stats">
                            <div class="stat">
                                <span class="stat-number">80+</span>
                                <span class="stat-label">Happy Clients</span>
                            </div>
                            <div class="stat">
                                <span class="stat-number">150+</span>
                                <span class="stat-label">Projects Completed</span>
                            </div>
                            <div class="stat">
                                <span class="stat-number">5+</span>
                                <span class="stat-label">Years Experience</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="about-image">
                        <img src="images/about-image.jpg" alt="Larry Bronx working">
                    </div>
                </div>
            </div>
        </section>

        <!-- Stack Section -->
        <section id="stack" class="stack-section">
            <div class="container">
                <div class="section-header">
                    <h2>My Tech Stack</h2>
                    <p class="section-subtitle">Technologies I work with</p>
                </div>
                
                <div class="stack-grid">
                    <div class="stack-item">
                        <img src="images/html5.svg" alt="HTML5">
                        <span>HTML5</span>
                    </div>
                    <div class="stack-item">
                        <img src="images/css3.svg" alt="CSS3">
                        <span>CSS3</span>
                    </div>
                    <div class="stack-item">
                        <img src="images/javascript.svg" alt="JavaScript">
                        <span>JavaScript</span>
                    </div>
                    <div class="stack-item">
                        <img src="images/react.svg" alt="React">
                        <span>React</span>
                    </div>
                    <div class="stack-item">
                        <img src="images/figma.svg" alt="Figma">
                        <span>Figma</span>
                    </div>
                    <div class="stack-item">
                        <img src="images/adobe-xd.svg" alt="Adobe XD">
                        <span>Adobe XD</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Services Section -->
        <section id="services" class="services-section">
            <div class="container">
                <div class="section-header">
                    <h2>Services</h2>
                    <p class="section-subtitle">What I can do for you</p>
                </div>
                
                <div class="services-grid">
                    <div class="service-card">
                        <div class="service-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                                <circle cx="9" cy="9" r="2"/>
                                <path d="M21 15l-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
                            </svg>
                        </div>
                        <h3>UX/UI Design</h3>
                        <p>Creating intuitive and engaging user experiences through research, wireframing, and prototyping.</p>
                    </div>
                    
                    <div class="service-card">
                        <div class="service-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="16 18 22 12 16 6"/>
                                <polyline points="8 6 2 12 8 18"/>
                            </svg>
                        </div>
                        <h3>Frontend Development</h3>
                        <p>Building responsive and performant web applications using modern technologies and best practices.</p>
                    </div>
                    
                    <div class="service-card">
                        <div class="service-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="3"/>
                                <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                            </svg>
                        </div>
                        <h3>Product Design</h3>
                        <p>Designing complete digital products from concept to launch with focus on user needs and business goals.</p>
                    </div>
                    
                    <div class="service-card">
                        <div class="service-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                        </div>
                        <h3>Branding</h3>
                        <p>Creating cohesive brand identities that resonate with your target audience and stand out in the market.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Projects Section -->
        <section id="projects" class="projects-section">
            <div class="container">
                <div class="section-header">
                    <h2>Featured Projects</h2>
                    <p class="section-subtitle">Some of my recent work</p>
                </div>
                
                <div class="projects-grid">
                    <div class="project-card">
                        <div class="project-image">
                            <img src="images/project1.jpg" alt="Project 1">
                        </div>
                        <div class="project-content">
                            <h3>E-commerce Platform</h3>
                            <p>A modern e-commerce platform with intuitive user experience and seamless checkout process.</p>
                            <div class="project-tags">
                                <span class="tag">React</span>
                                <span class="tag">Node.js</span>
                                <span class="tag">MongoDB</span>
                            </div>
                            <a href="#" class="project-link">View Project</a>
                        </div>
                    </div>
                    
                    <div class="project-card">
                        <div class="project-image">
                            <img src="images/project2.jpg" alt="Project 2">
                        </div>
                        <div class="project-content">
                            <h3>Mobile Banking App</h3>
                            <p>A secure and user-friendly mobile banking application with advanced features.</p>
                            <div class="project-tags">
                                <span class="tag">React Native</span>
                                <span class="tag">UI/UX</span>
                                <span class="tag">Figma</span>
                            </div>
                            <a href="#" class="project-link">View Project</a>
                        </div>
                    </div>
                    
                    <div class="project-card">
                        <div class="project-image">
                            <img src="images/project3.jpg" alt="Project 3">
                        </div>
                        <div class="project-content">
                            <h3>SaaS Dashboard</h3>
                            <p>A comprehensive dashboard for SaaS applications with data visualization and analytics.</p>
                            <div class="project-tags">
                                <span class="tag">Vue.js</span>
                                <span class="tag">D3.js</span>
                                <span class="tag">API</span>
                            </div>
                            <a href="#" class="project-link">View Project</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section id="contact" class="contact-section">
            <div class="container">
                <div class="section-header">
                    <h2>Let's Work Together</h2>
                    <p class="section-subtitle">Ready to start your next project?</p>
                </div>
                
                <div class="contact-content">
                    <div class="contact-info">
                        <h3>Get in touch</h3>
                        <p>I'm always interested in new opportunities and exciting projects. Let's discuss how we can work together.</p>
                        
                        <div class="contact-methods">
                            <div class="contact-method">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                                    <polyline points="22,6 12,13 2,6"/>
                                </svg>
                                <span><EMAIL></span>
                            </div>
                            <div class="contact-method">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                                </svg>
                                <span>+1 (555) 123-4567</span>
                            </div>
                        </div>
                    </div>
                    
                    <form class="contact-form">
                        <div class="form-group">
                            <input type="text" id="name" name="name" placeholder="Your Name" required>
                        </div>
                        <div class="form-group">
                            <input type="email" id="email" name="email" placeholder="Your Email" required>
                        </div>
                        <div class="form-group">
                            <textarea id="message" name="message" placeholder="Your Message" rows="5" required></textarea>
                        </div>
                        <button type="submit" class="submit-btn">Send Message</button>
                    </form>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 Larry Bronx. All rights reserved.</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="script.js"></script>
</body>
</html>
